# SMS4J 3.3.5 短信中间件配置和使用说明

## 📱 **配置概览**

SMS4J 3.3.5是一个强大的短信聚合框架，本项目配置为**纯中间件模式**，专注于短信发送功能，不处理业务逻辑。

### 🎯 **中间件特点**
- ✅ 纯接口调用，无业务逻辑
- ✅ 不依赖Redis等外部存储
- ✅ 轻量级部署
- ✅ 专注短信发送功能
- ✅ 由调用方控制验证码验证和频率限制

### 🔧 **已配置的短信服务商**

1. **阿里云短信** (默认启用)
2. **腾讯云短信** (可选)
3. **华为云短信** (可选)
4. **容联云短信** (可选)
5. **网易云信短信** (可选)
6. **京东云短信** (可选)

### 🆕 **SMS4J 3.x 新特性**

- 支持单厂商多配置
- 负载均衡
- 短信拦截和限制
- 异步短信发送
- 延迟短信发送
- HTTP代理支持
- 更简洁的配置方式

## 📋 **配置文件说明**

### 1. **主配置文件** (`application-sms.yml`)

```yaml
sms:
  # 配置类型：yaml表示从yml读取配置
  config-type: yaml
  # 是否开启短信拦截（全局配置）
  restricted: false
  # 短信拦截限制单手机号每日最大发送量
  account-max: 50
  # 短信拦截限制单手机号每分钟最大发送
  minute-max: 5
  # 线程池配置（用于异步短信）
  core-pool-size: 10
  max-pool-size: 30
  queue-capacity: 50
  # 是否打印HTTP日志
  http-log: false
  # 是否打印banner
  is-print: true
```

### 2. **阿里云短信配置**

```yaml
sms:
  blends:
    aliyun-main:                          # 自定义配置标识
      supplier: alibaba                   # 厂商标识
      access-key-id: ${SMS_ALIYUN_ACCESS_KEY_ID}
      access-key-secret: ${SMS_ALIYUN_ACCESS_KEY_SECRET}
      signature: ${SMS_ALIYUN_SIGNATURE}  # 短信签名
      template-id: ${SMS_ALIYUN_TEMPLATE_ID}  # 模板ID（可选）
      request-url: dysmsapi.aliyuncs.com  # 请求地址（可选）
      restricted: false                   # 是否开启此配置的短信拦截
```

### 3. **环境变量配置**

建议在环境变量中配置敏感信息：

```bash
# 阿里云短信配置
export SMS_ALIYUN_ACCESS_KEY_ID="your_access_key_id"
export SMS_ALIYUN_ACCESS_KEY_SECRET="your_access_key_secret"
export SMS_ALIYUN_SIGN_NAME="德阳市中江县人民医院"

# 腾讯云短信配置
export SMS_TENCENT_SECRET_ID="your_secret_id"
export SMS_TENCENT_SECRET_KEY="your_secret_key"
export SMS_TENCENT_APP_ID="your_app_id"
```

## 🚀 **使用方式**

### 1. **通过REST API调用**

#### **发送验证码**
```http
POST /sms/sendCode
Content-Type: application/json

{
    "phone": "***********",
    "businessType": "login",
    "codeLength": 6,
    "expireMinutes": 5
}
```

#### **发送简单验证码**
```http
POST /sms/sendSimpleCode?phone=***********&code=123456
```

#### **发送登录通知**
```http
POST /sms/sendLoginNotice?phone=***********&loginTime=2024-01-01 10:00:00&loginLocation=北京
```

#### **发送系统通知**
```http
POST /sms/sendSystemNotice?phone=***********&content=您的账户余额不足，请及时充值
```

#### **发送预约提醒**
```http
POST /sms/sendAppointmentReminder?phone=***********&patientName=张三&appointmentTime=2024-01-02 09:00&department=内科
```

### 2. **通过Java代码调用**

#### **使用SmsService服务**
```java
@Autowired
private SmsService smsService;

// 发送验证码
public void sendCode() {
    VerificationCodeDto dto = VerificationCodeDto.createLoginCode("***********");
    boolean result = smsService.sendVerificationCode(dto);
}

// 发送登录通知
public void sendLoginNotice() {
    boolean result = smsService.sendLoginNotice("***********", "2024-01-01 10:00:00", "北京");
}
```

#### **使用SmsUtil工具类**
```java
// 发送验证码
String code = SmsUtil.generateAndSendCode("***********");

// 注意：中间件模式不提供验证码验证功能
// 验证码验证需要在调用方（业务系统）中实现

// 发送登录通知
SmsUtil.sendLoginNotice("***********", "2024-01-01 10:00:00", "北京");

// 发送系统通知
SmsUtil.sendSystemNotice("***********", "系统维护通知");

// 使用指定配置发送
SmsUtil.sendSms("tencent-main", "***********", "使用腾讯云发送");
```

#### **直接使用SMS4J 3.x API**
```java
public void sendSms() {
    // 获取指定配置的短信实例
    SmsBlend smsBlend = SmsFactory.getSmsBlend("aliyun-main");

    // 发送文本短信（快速发送）
    SmsResponse response = smsBlend.sendMessage("***********", "您的验证码是123456");

    // 发送模板短信
    Map<String, Object> params = Map.of("code", "123456");
    SmsResponse response2 = smsBlend.sendMessage("***********", "SMS_123456789", params);

    // 异步发送短信
    smsBlend.sendMessageAsync("***********", "异步发送的短信");

    // 负载均衡发送（自动选择配置）
    SmsBlend balancedBlend = SmsFactory.getSmsBlend();
    balancedBlend.sendMessage("***********", "负载均衡发送");
}
```

### 3. **批量发送短信**

```java
List<SmsDto> smsList = Arrays.asList(
    SmsDto.createVerificationCode("***********", "123456"),
    SmsDto.createVerificationCode("***********", "654321")
);

boolean result = smsService.batchSendSms(smsList);
```

## 🔒 **安全特性（中间件模式）**

### 1. **发送频率限制**
- 中间件模式：关闭内置频率限制（`account-max: 0`, `minute-max: 0`）
- 频率控制由调用方（业务系统）负责
- 支持SMS4J内置黑名单功能

### 2. **验证码管理**
- 中间件只负责发送验证码
- 验证码的存储、验证、过期管理由调用方负责
- 不依赖Redis等外部存储

### 3. **黑名单功能**
```java
SmsBlend smsBlend = SmsFactory.getSmsBlend("aliyun-main");
// 添加黑名单
smsBlend.joinInBlacklist("***********");
// 移除黑名单
smsBlend.removeFromBlacklist("***********");
// 批量操作
smsBlend.batchJoinBlacklist(Arrays.asList("***********", "***********"));
```

### 4. **HTTP代理支持**
```yaml
sms:
  blends:
    aliyun-main:
      proxy:
        enable: true
        host: 127.0.0.1
        port: 8080
```

## 📊 **监控和统计**

### 1. **发送记录**
```yaml
sms:
  record:
    enabled: true                # 启用发送记录
    storage-type: database       # 存储方式：database/file/redis
    retention-days: 30           # 记录保留天数
```

### 2. **发送统计**
```yaml
sms:
  statistics:
    enabled: true                # 启用统计
    storage-type: redis          # 统计存储方式
    period: 60                   # 统计周期(分钟)
```

## 🛠️ **故障排查**

### 1. **常见问题**

#### **短信发送失败**
- 检查短信服务商配置是否正确
- 确认短信模板是否已审核通过
- 检查账户余额是否充足
- 验证手机号格式是否正确

#### **验证码验证失败**
- 确认Redis是否正常运行
- 检查验证码是否已过期
- 验证验证码是否正确

### 2. **日志查看**
```yaml
logging:
  level:
    org.dromara.sms4j: DEBUG     # 开启SMS4J调试日志
    com.jp.med.mid.modules.sms: DEBUG  # 开启短信模块调试日志
```

### 3. **健康检查**
```http
GET /sms/balance                 # 查询短信余额
GET /sms/status/{messageId}      # 查询发送状态
```

## 🔄 **扩展配置**

### 1. **添加新的短信服务商**
1. 添加对应的SMS4J插件依赖
2. 在配置文件中添加服务商配置
3. 重启应用即可

### 2. **自定义短信模板**
```yaml
sms:
  aliyun:
    templates:
      custom-template: SMS_XXXXXX  # 自定义模板ID
```

### 3. **多环境配置**
- `application-sms-dev.yml` - 开发环境
- `application-sms-test.yml` - 测试环境  
- `application-sms-prod.yml` - 生产环境

## 📞 **技术支持**

如有问题，请联系技术支持团队或查看SMS4J官方文档：
- SMS4J官网：https://sms4j.com/
- 项目地址：https://gitee.com/dromara/sms4j

## 🎯 **最佳实践（中间件模式）**

### 1. **中间件部署建议**：
   - 使用环境变量配置敏感信息
   - 关闭内置频率限制：`restricted: false`
   - 使用多厂商配置实现容灾
   - 启用HTTP日志：`http-log: true`（调试时）
   - 轻量级部署，无需Redis等外部依赖

### 2. **调用方（业务系统）责任**：
   - 实现验证码的生成、存储、验证逻辑
   - 控制短信发送频率和限制
   - 处理用户黑名单管理
   - 实现业务相关的短信模板管理

### 3. **安全建议**：
   - 定期更换AccessKey
   - 在调用方实现发送频率控制
   - 在调用方实现IP白名单等访问控制
   - 使用HTTPS调用中间件接口
   - 配置HTTP代理（如需要）

### 4. **性能优化**：
   - 使用异步发送：`sendMessageAsync()`
   - 配置合适的线程池大小
   - 使用负载均衡：`SmsFactory.getSmsBlend()`（无参）
   - 缓存SmsBlend实例避免重复创建

### 5. **多厂商配置示例**：
```yaml
sms:
  blends:
    aliyun-main:
      supplier: alibaba
      # 阿里云配置...
    aliyun-backup:
      supplier: alibaba
      # 阿里云备用配置...
    tencent-main:
      supplier: tencent
      # 腾讯云配置...
```

### 6. **错误处理**：
```java
try {
    SmsBlend smsBlend = SmsFactory.getSmsBlend("aliyun-main");
    SmsResponse response = smsBlend.sendMessage(phone, message);

    if (response.isSuccess()) {
        log.info("短信发送成功：{}", response.getData());
    } else {
        log.error("短信发送失败：{}", response.getMessage());
        // 可以尝试使用备用配置
        SmsBlend backupBlend = SmsFactory.getSmsBlend("aliyun-backup");
        backupBlend.sendMessage(phone, message);
    }
} catch (Exception e) {
    log.error("短信发送异常", e);
}
```
