# SMS4J 3.3.5 短信配置文件 - 中间件模式（无Redis）
sms:
  # 配置类型：yaml表示从yml读取配置
  config-type: yaml

  # 中间件模式：关闭短信拦截（由调用方控制）
  restricted: false

  # 不设置发送限制（由调用方控制）
  account-max: 0
  minute-max: 0

  # 线程池配置（用于异步短信）
  # 核心线程池大小
  core-pool-size: 10
  # 最大线程数
  max-pool-size: 30
  # 队列容量
  queue-capacity: 50
  # 设置线程池关闭的时候等待所有任务都完成再继续销毁其他的Bean
  shutdown-strategy: true

  # 是否打印HTTP日志
  http-log: false

  # 是否打印banner
  is-print: true

  # 短信厂商配置（blends配置）
  blends:
    # 阿里云短信配置（自定义标识，可以是任意值）
    aliyun-main:
      # 厂商标识，标定此配置是哪个厂商
      supplier: alibaba
      # Access Key ID
      access-key-id: ${SMS_ALIYUN_ACCESS_KEY_ID:LTAI5tDJkxFcR6DGmjAet3hd}
      # Access Key Secret
      access-key-secret: ${SMS_ALIYUN_ACCESS_KEY_SECRET:******************************}
      # 短信签名
      signature: ${SMS_ALIYUN_SIGNATURE:德阳市中江县人民医院}
      # 模板ID（用于快速发送，可选）
      template-id: ${SMS_ALIYUN_TEMPLATE_ID:SMS_123456789}
      # 请求地址（默认为dysmsapi.aliyuncs.com，可选）
      request-url: dysmsapi.aliyuncs.com
      # 是否开启此配置的短信拦截
      restricted: false
      # HTTP代理配置（可选）
      proxy:
        # 是否启用代理，默认关闭
        enable: false
        host: 127.0.0.1
        port: 8080

    # 腾讯云短信配置（自定义标识）
    tencent-main:
      # 厂商标识
      supplier: tencent
      # SecretId
      access-key-id: ${SMS_TENCENT_SECRET_ID:your_secret_id}
      # SecretKey
      access-key-secret: ${SMS_TENCENT_SECRET_KEY:your_secret_key}
      # 短信应用ID
      sdk-app-id: ${SMS_TENCENT_APP_ID:your_app_id}
      # 短信签名
      signature: ${SMS_TENCENT_SIGNATURE:德阳市中江县人民医院}
      # 模板ID（用于快速发送，可选）
      template-id: ${SMS_TENCENT_TEMPLATE_ID:123456}
      # 地域
      territory: ap-beijing
      # 是否开启此配置的短信拦截
      restricted: false
      # HTTP代理配置（可选）
      proxy:
        enable: false
        host: 127.0.0.1
        port: 8080

    # 华为云短信配置（自定义标识）
    huawei-main:
      # 厂商标识
      supplier: huawei
      # App Key
      app-key: ${SMS_HUAWEI_APP_KEY:your_app_key}
      # App Secret
      app-secret: ${SMS_HUAWEI_APP_SECRET:your_app_secret}
      # 短信签名
      signature: ${SMS_HUAWEI_SIGNATURE:德阳市中江县人民医院}
      # 短信通道号
      sender: ${SMS_HUAWEI_SENDER:your_sender}
      # 模板ID（用于快速发送，可选）
      template-id: ${SMS_HUAWEI_TEMPLATE_ID:your_template_id}
      # 华为分配的app请求地址
      url: ${SMS_HUAWEI_URL:https://smsapi.cn-north-4.myhuaweicloud.com:443}
      # 华为回调地址（可选）
      status-call-back: ${SMS_HUAWEI_CALLBACK:}
      # 是否开启此配置的短信拦截
      restricted: false

    # 容联云短信配置（自定义标识）
    cloopen-main:
      # 厂商标识
      supplier: cloopen
      # 账户SID
      access-key-id: ${SMS_CLOOPEN_ACCOUNT_SID:your_account_sid}
      # 认证令牌
      access-key-secret: ${SMS_CLOOPEN_AUTH_TOKEN:your_auth_token}
      # 应用ID
      app-id: ${SMS_CLOOPEN_APP_ID:your_app_id}
      # 模板ID（用于快速发送，可选）
      template-id: ${SMS_CLOOPEN_TEMPLATE_ID:123456}
      # 是否开启此配置的短信拦截
      restricted: false

    # 网易云信短信配置（自定义标识）
    netease-main:
      # 厂商标识
      supplier: netease
      # App Key
      app-key: ${SMS_NETEASE_APP_KEY:your_app_key}
      # App Secret
      app-secret: ${SMS_NETEASE_APP_SECRET:your_app_secret}
      # 模板ID（用于快速发送，可选）
      template-id: ${SMS_NETEASE_TEMPLATE_ID:123456}
      # 是否开启此配置的短信拦截
      restricted: false

    # 京东云短信配置（自定义标识）
    jdcloud-main:
      # 厂商标识
      supplier: jdcloud
      # Access Key ID
      access-key-id: ${SMS_JDCLOUD_ACCESS_KEY_ID:your_access_key_id}
      # Secret Access Key
      access-key-secret: ${SMS_JDCLOUD_SECRET_ACCESS_KEY:your_secret_access_key}
      # 地域
      region: cn-north-1
      # 短信签名ID
      sign-id: ${SMS_JDCLOUD_SIGN_ID:your_sign_id}
      # 模板ID（用于快速发送，可选）
      template-id: ${SMS_JDCLOUD_TEMPLATE_ID:your_template_id}
      # 是否开启此配置的短信拦截
      restricted: false
