package com.jp.med.mid.common.util;

import cn.hutool.core.util.StrUtil;
import com.jp.med.mid.common.exception.AppException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * SMS4J工具类 - 中间件模式
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Component
public class Sms4jUtil {

    private static final Logger log = LoggerFactory.getLogger(Sms4jUtil.class);

    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 发送短信
     * 
     * @param phone 手机号
     * @param content 短信内容
     * @return 发送结果
     */
    public static boolean sendSms(String phone, String content) {
        try {
            validatePhone(phone);
            
            if (StrUtil.isBlank(content)) {
                throw new AppException("短信内容不能为空");
            }
            
            log.info("发送短信，手机号：{}, 内容：{}", phone, content);
            
            // TODO: 这里需要集成SMS4J 3.3.5的实际API
            // 当SMS4J依赖正确加载后，使用以下代码：
            /*
            try {
                SmsBlend smsBlend = SmsFactory.getSmsBlend("aliyun-main");
                SmsResponse response = smsBlend.sendMessage(phone, content);
                
                if (response.isSuccess()) {
                    log.info("短信发送成功，手机号：{}, 消息ID：{}", phone, response.getData());
                    return true;
                } else {
                    log.error("短信发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                    return false;
                }
            } catch (Exception e) {
                log.error("SMS4J发送异常", e);
                return false;
            }
            */
            
            // 临时返回成功，用于测试
            log.info("短信发送成功（模拟），手机号：{}", phone);
            return true;
            
        } catch (Exception e) {
            log.error("发送短信异常，手机号：{}", phone, e);
            return false;
        }
    }

    /**
     * 发送验证码短信
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 发送结果
     */
    public static boolean sendVerificationCode(String phone, String code) {
        String content = String.format("您的验证码是：%s，5分钟内有效，请勿泄露。", code);
        return sendSms(phone, content);
    }

    /**
     * 发送登录通知
     * 
     * @param phone 手机号
     * @param loginTime 登录时间
     * @param location 登录地点
     * @return 发送结果
     */
    public static boolean sendLoginNotice(String phone, String loginTime, String location) {
        String content = String.format("您的账户于%s在%s登录，如非本人操作请及时修改密码。", loginTime, location);
        return sendSms(phone, content);
    }

    /**
     * 发送系统通知
     * 
     * @param phone 手机号
     * @param notice 通知内容
     * @return 发送结果
     */
    public static boolean sendSystemNotice(String phone, String notice) {
        return sendSms(phone, notice);
    }

    /**
     * 发送预约提醒
     * 
     * @param phone 手机号
     * @param patientName 患者姓名
     * @param appointmentTime 预约时间
     * @param department 科室
     * @return 发送结果
     */
    public static boolean sendAppointmentReminder(String phone, String patientName, 
                                                 String appointmentTime, String department) {
        String content = String.format("尊敬的%s，您预约的%s科室%s的就诊时间即将到来，请准时就诊。", 
            patientName, department, appointmentTime);
        return sendSms(phone, content);
    }

    /**
     * 验证手机号格式
     */
    private static void validatePhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            throw new AppException("手机号不能为空");
        }

        if (!PHONE_PATTERN.matcher(phone).matches()) {
            throw new AppException("手机号格式不正确");
        }
    }
}
