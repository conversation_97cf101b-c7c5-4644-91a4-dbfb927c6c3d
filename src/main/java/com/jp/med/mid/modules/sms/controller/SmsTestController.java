package com.jp.med.mid.modules.sms.controller;

import com.jp.med.mid.common.entity.CommonResult;
import com.jp.med.mid.common.util.Sms4jUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * SMS测试控制器 - 用于测试SMS功能
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/sms/test")
public class SmsTestController {

    /**
     * 测试发送短信
     */
    @PostMapping("/send")
    public CommonResult<Boolean> testSendSms(
            @RequestParam String phone,
            @RequestParam String content) {
        log.info("测试发送短信，手机号：{}, 内容：{}", phone, content);
        try {
            boolean result = Sms4jUtil.sendSms(phone, content);
            return result ? CommonResult.success(true, "短信发送成功") 
                         : CommonResult.failed("短信发送失败");
        } catch (Exception e) {
            log.error("测试发送短信异常", e);
            return CommonResult.failed("短信发送异常：" + e.getMessage());
        }
    }

    /**
     * 测试发送验证码
     */
    @PostMapping("/code")
    public CommonResult<Boolean> testSendCode(
            @RequestParam String phone,
            @RequestParam String code) {
        log.info("测试发送验证码，手机号：{}, 验证码：{}", phone, code);
        try {
            boolean result = Sms4jUtil.sendVerificationCode(phone, code);
            return result ? CommonResult.success(true, "验证码发送成功") 
                         : CommonResult.failed("验证码发送失败");
        } catch (Exception e) {
            log.error("测试发送验证码异常", e);
            return CommonResult.failed("验证码发送异常：" + e.getMessage());
        }
    }

    /**
     * 测试发送登录通知
     */
    @PostMapping("/login")
    public CommonResult<Boolean> testSendLoginNotice(
            @RequestParam String phone,
            @RequestParam String loginTime,
            @RequestParam String location) {
        log.info("测试发送登录通知，手机号：{}", phone);
        try {
            boolean result = Sms4jUtil.sendLoginNotice(phone, loginTime, location);
            return result ? CommonResult.success(true, "登录通知发送成功") 
                         : CommonResult.failed("登录通知发送失败");
        } catch (Exception e) {
            log.error("测试发送登录通知异常", e);
            return CommonResult.failed("登录通知发送异常：" + e.getMessage());
        }
    }

    /**
     * 测试发送系统通知
     */
    @PostMapping("/notice")
    public CommonResult<Boolean> testSendSystemNotice(
            @RequestParam String phone,
            @RequestParam String notice) {
        log.info("测试发送系统通知，手机号：{}", phone);
        try {
            boolean result = Sms4jUtil.sendSystemNotice(phone, notice);
            return result ? CommonResult.success(true, "系统通知发送成功") 
                         : CommonResult.failed("系统通知发送失败");
        } catch (Exception e) {
            log.error("测试发送系统通知异常", e);
            return CommonResult.failed("系统通知发送异常：" + e.getMessage());
        }
    }

    /**
     * 测试发送预约提醒
     */
    @PostMapping("/appointment")
    public CommonResult<Boolean> testSendAppointmentReminder(
            @RequestParam String phone,
            @RequestParam String patientName,
            @RequestParam String appointmentTime,
            @RequestParam String department) {
        log.info("测试发送预约提醒，手机号：{}, 患者：{}", phone, patientName);
        try {
            boolean result = Sms4jUtil.sendAppointmentReminder(phone, patientName, appointmentTime, department);
            return result ? CommonResult.success(true, "预约提醒发送成功") 
                         : CommonResult.failed("预约提醒发送失败");
        } catch (Exception e) {
            log.error("测试发送预约提醒异常", e);
            return CommonResult.failed("预约提醒发送异常：" + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public CommonResult<String> health() {
        return CommonResult.success("SMS服务运行正常", "SMS Test Controller is running");
    }
}
