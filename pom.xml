<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.12.RELEASE</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.jp.med</groupId>
	<artifactId>med-mid</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>med-mid</name>
	<description>med-mid</description>
	<properties>
		<java.version>11</java.version>
<!--		<oracle.version>********</oracle.version>-->
		<pg.version>42.5.4</pg.version>
		<sqlserver.version>12.6.1.jre11</sqlserver.version>
		<quartz.version>2.3.0</quartz.version>
		<commons.lang.version>2.6</commons.lang.version>
		<commons.fileupload.version>1.2.2</commons.fileupload.version>
		<commons.io.version>2.5</commons.io.version>
		<commons.codec.version>1.10</commons.codec.version>
		<commons.configuration.version>1.10</commons.configuration.version>
		<jwt.version>0.7.0</jwt.version>
		<kaptcha.version>0.0.9</kaptcha.version>
		<qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
		<aliyun.oss.version>2.8.3</aliyun.oss.version>
		<qcloud.cos.version>4.4</qcloud.cos.version>
		<joda.time.version>2.9.9</joda.time.version>
		<fastjson.version>1.2.60</fastjson.version>
		<hutool.version>5.8.16</hutool.version>
		<goole-gson.version>2.8.5</goole-gson.version>
		<easy-captcha.version>1.6.2</easy-captcha.version>
		<druid.version>1.1.10</druid.version>
		<mybatis-plus.version>3.5.3</mybatis-plus.version>
		<pagehelper.version>1.4.6</pagehelper.version>
		<sms4j.version>3.3.5</sms4j.version>
	</properties>
	<dependencies>
		<!-- starter  begin-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- starter  end-->

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>${goole-gson.version}</version>
		</dependency>

		<!--oracle驱动-->
<!--		<dependency>-->
<!--			<groupId>com.oracle</groupId>-->
<!--			<artifactId>ojdbc6</artifactId>-->
<!--			<version>${oracle.version}</version>-->
<!--		</dependency>-->

		<!-- pg驱动 -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>

		<!--sqlServer驱动-->
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
			<version>${sqlserver.version}</version> <!-- 使用适当的版本号 -->
		</dependency>

		<!-- commons begin -->
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>${commons.lang.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>${commons.fileupload.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons.io.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>${commons.codec.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-configuration</groupId>
			<artifactId>commons-configuration</artifactId>
			<version>${commons.configuration.version}</version>
		</dependency>
		<!-- commons end -->

		<!-- jwt -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>${jwt.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>nimbus-jose-jwt</artifactId>
			<version>8.16</version>
		</dependency>


		<!-- kaptcha -->
		<dependency>
			<groupId>com.github.axet</groupId>
			<artifactId>kaptcha</artifactId>
			<version>${kaptcha.version}</version>
		</dependency>

		<!-- fastjson -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>

		<!-- hutool -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>${hutool.version}</version>
		</dependency>

		<!-- 验证码 -->
		<dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
            <version>${easy-captcha.version}</version>
        </dependency>

		<!--集成druid连接池-->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>${druid.version}</version>
		</dependency>

		<!-- mybatis-plus-->
		<dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
            <exclusions>
				<exclusion>
					<groupId>com.baomidou</groupId>
					<artifactId>mybatis-plus-generator</artifactId>
				</exclusion>
			</exclusions>
        </dependency>

		<!-- 个推 -->
		<dependency>
			<groupId>com.getui.push</groupId>
			<artifactId>restful-sdk</artifactId>
			<version>1.0.0.1</version>
		</dependency>

		<dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

		<dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>

		<!-- 阿里云发票核验 -->
		<!--<dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>ocr_api20210707</artifactId>
		  <version>2.0.1</version>
		</dependency>-->
		<dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>ocr_api20210707</artifactId>
		  <version>3.1.2</version>
		</dependency>
		<dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>tea-openapi</artifactId>
		  <version>0.3.1</version>
		</dependency>
		<dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>tea-console</artifactId>
		  <version>0.0.1</version>
		</dependency>
		<dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>tea-util</artifactId>
		  <version>0.2.21</version>
		</dependency>
		<dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>darabonba-stream</artifactId>
		  <version>0.0.1</version>
		</dependency>

		<dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>alibabacloud-ocr_api20210707</artifactId>
		  <version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>


		<!-- SMS4J 3.3.5 核心依赖 -->
		<dependency>
			<groupId>org.dromara.sms4j</groupId>
			<artifactId>sms4j-spring-boot-starter</artifactId>
			<version>${sms4j.version}</version>
		</dependency>

		<!-- 阿里云短信插件 -->
		<dependency>
			<groupId>org.dromara.sms4j</groupId>
			<artifactId>sms4j-aliyun-plugin</artifactId>
			<version>${sms4j.version}</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.example</groupId>
				<artifactId>mixed-plugin</artifactId>
				<version>1.0.0</version>
				<executions>
					<execution>
						<goals>
							<goal>build</goal>
						</goals>
						<phase>package</phase>
						<configuration>
							<source>${project.basedir}/target/${project.artifactId}-${project.version}.jar</source>
							<target>${project.basedir}/target/${project.artifactId}.jar</target>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
