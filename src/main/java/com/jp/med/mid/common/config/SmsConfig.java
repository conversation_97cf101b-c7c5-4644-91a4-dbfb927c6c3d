package com.jp.med.mid.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * SMS4J短信配置类
 *
 * <AUTHOR>
 * @date 2024-01-01
 * @version 3.3.5
 */
@Slf4j
@Configuration
public class SmsConfig {

    /**
     * SMS4J 3.x版本通过yml配置自动初始化，无需手动创建Bean
     * SmsFactory.getSmsBlend(configId) 可以直接获取配置的短信实例
     */

    public SmsConfig() {
        log.info("SMS4J 3.3.5 短信配置类初始化完成");
        log.info("请确保在application-sms.yml中正确配置了短信厂商信息");
    }
}
