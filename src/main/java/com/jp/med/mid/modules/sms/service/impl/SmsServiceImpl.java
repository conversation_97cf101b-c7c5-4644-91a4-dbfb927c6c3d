package com.jp.med.mid.modules.sms.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.jp.med.mid.common.exception.AppException;
import com.jp.med.mid.common.util.Sms4jUtil;
import com.jp.med.mid.modules.sms.dto.SmsDto;
import com.jp.med.mid.modules.sms.dto.VerificationCodeDto;
import com.jp.med.mid.modules.sms.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 短信服务实现类 - 中间件模式
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    @Override
    public boolean sendSms(SmsDto smsDto) {
        try {
            log.info("开始发送短信，手机号：{}, 内容：{}", smsDto.getPhone(),
                    StrUtil.isNotBlank(smsDto.getContent()) ? smsDto.getContent() : "模板短信");

            if (StrUtil.isBlank(smsDto.getPhone())) {
                throw new AppException("手机号不能为空");
            }

            String content = smsDto.getContent();
            if (StrUtil.isBlank(content)) {
                // 如果没有直接内容，尝试从模板参数构建
                if (smsDto.getTemplateParams() != null && !smsDto.getTemplateParams().isEmpty()) {
                    content = smsDto.getTemplateParams().values().iterator().next().toString();
                } else {
                    throw new AppException("短信内容不能为空");
                }
            }

            return Sms4jUtil.sendSms(smsDto.getPhone(), content);

        } catch (Exception e) {
            log.error("短信发送异常，手机号：{}", smsDto.getPhone(), e);
            throw new AppException("短信发送失败：" + e.getMessage());
        }
    }

    @Override
    public boolean sendVerificationCode(String phone, String code) {
        try {
            log.info("开始发送验证码，手机号：{}, 验证码：{}", phone, code);

            if (StrUtil.isBlank(phone)) {
                throw new AppException("手机号不能为空");
            }

            if (StrUtil.isBlank(code)) {
                throw new AppException("验证码不能为空");
            }

            return Sms4jUtil.sendVerificationCode(phone, code);

        } catch (Exception e) {
            log.error("验证码发送异常，手机号：{}", phone, e);
            throw new AppException("验证码发送失败：" + e.getMessage());
        }
    }

    @Override
    public boolean sendVerificationCode(VerificationCodeDto verificationCodeDto) {
        // 生成验证码
        String code = generateVerificationCode(verificationCodeDto.getCodeType(), verificationCodeDto.getCodeLength());
        verificationCodeDto.setCode(code);

        log.info("生成验证码，业务类型：{}, 手机号：{}, 验证码长度：{}",
                verificationCodeDto.getBusinessType(), verificationCodeDto.getPhone(), verificationCodeDto.getCodeLength());

        // 发送短信
        boolean result = sendVerificationCode(verificationCodeDto.getPhone(), code);

        if (result) {
            log.info("验证码发送成功，业务类型：{}, 手机号：{}, 验证码：{}",
                    verificationCodeDto.getBusinessType(), verificationCodeDto.getPhone(), code);
        }

        return result;
    }

    @Override
    public boolean sendLoginNotice(String phone, String loginTime, String loginLocation) {
        try {
            return Sms4jUtil.sendLoginNotice(phone, loginTime, loginLocation);
        } catch (Exception e) {
            log.error("登录通知发送异常，手机号：{}", phone, e);
            return false;
        }
    }

    @Override
    public boolean sendSystemNotice(String phone, String content) {
        try {
            return Sms4jUtil.sendSystemNotice(phone, content);
        } catch (Exception e) {
            log.error("系统通知发送异常，手机号：{}", phone, e);
            return false;
        }
    }

    @Override
    public boolean sendPasswordReset(String phone, String resetCode) {
        try {
            return Sms4jUtil.sendVerificationCode(phone, resetCode);
        } catch (Exception e) {
            log.error("密码重置短信发送异常，手机号：{}", phone, e);
            return false;
        }
    }

    @Override
    public boolean sendAppointmentReminder(String phone, String patientName, String appointmentTime, String department) {
        try {
            return Sms4jUtil.sendAppointmentReminder(phone, patientName, appointmentTime, department);
        } catch (Exception e) {
            log.error("预约提醒发送异常，手机号：{}", phone, e);
            return false;
        }
    }

    @Override
    public boolean batchSendSms(List<SmsDto> smsDtoList) {
        try {
            log.info("开始批量发送短信，数量：{}", smsDtoList.size());

            int successCount = 0;
            for (SmsDto smsDto : smsDtoList) {
                try {
                    if (sendSms(smsDto)) {
                        successCount++;
                    }
                    // 避免发送过快
                    Thread.sleep(100);
                } catch (Exception e) {
                    log.error("批量发送短信失败，手机号：{}", smsDto.getPhone(), e);
                }
            }

            log.info("批量发送短信完成，成功：{}/{}", successCount, smsDtoList.size());
            return successCount > 0;
        } catch (Exception e) {
            log.error("批量发送短信异常", e);
            return false;
        }
    }

    @Override
    public String querySmsStatus(String messageId) {
        try {
            // 这里需要根据具体的短信供应商实现状态查询
            // SMS4J框架可能需要扩展来支持状态查询
            log.info("查询短信状态，消息ID：{}", messageId);
            return "DELIVERED"; // 示例返回值
        } catch (Exception e) {
            log.error("查询短信状态失败，消息ID：{}", messageId, e);
            return "UNKNOWN";
        }
    }

    @Override
    public String getSmsBalance() {
        try {
            // 这里需要根据具体的短信供应商实现余额查询
            log.info("查询短信余额");
            return "余额充足"; // 示例返回值
        } catch (Exception e) {
            log.error("查询短信余额失败", e);
            return "查询失败";
        }
    }

    /**
     * 生成验证码
     */
    private String generateVerificationCode(Integer codeType, Integer codeLength) {
        switch (codeType) {
            case 1: // 纯数字
                return RandomUtil.randomNumbers(codeLength);
            case 2: // 纯字母
                return RandomUtil.randomString(codeLength).toUpperCase();
            case 3: // 数字+字母
                return RandomUtil.randomStringUpper(codeLength);
            default:
                return RandomUtil.randomNumbers(codeLength);
        }
    }
}
