# SMS4J 3.3.5 短信配置文件
sms:
  # 配置类型：yaml表示从yml读取配置
  config-type: yaml

  # 是否开启短信拦截（全局配置）
  restricted: false

  # 短信拦截限制单手机号每日最大发送量（只对开启了拦截的配置有效）
  account-max: 50

  # 短信拦截限制单手机号每分钟最大发送（只对开启了拦截的配置有效）
  minute-max: 5

  # 线程池配置（用于异步短信）
  # 核心线程池大小
  core-pool-size: 10
  # 最大线程数
  max-pool-size: 30
  # 队列容量
  queue-capacity: 50
  # 设置线程池关闭的时候等待所有任务都完成再继续销毁其他的Bean
  shutdown-strategy: true

  # 是否打印HTTP日志
  http-log: false

  # 是否打印banner
  is-print: true

  # 短信厂商配置（blends配置）
  blends:
    # 阿里云短信配置（自定义标识，可以是任意值）
    aliyun-main:
    # 是否启用
    enabled: true
    # Access Key ID
    access-key-id: ${SMS_ALIYUN_ACCESS_KEY_ID:LTAI5tDJkxFcR6DGmjAet3hd}
    # Access Key Secret
    access-key-secret: ${SMS_ALIYUN_ACCESS_KEY_SECRET:******************************}
    # 短信签名
    sign-name: ${SMS_ALIYUN_SIGN_NAME:德阳市中江县人民医院}
    # 地域节点
    region-id: cn-hangzhou
    # 短信模板配置
    templates:
      # 验证码模板
      verification-code: SMS_123456789
      # 登录通知模板
      login-notice: SMS_987654321
      # 系统通知模板
      system-notice: SMS_111222333
      # 密码重置模板
      password-reset: SMS_444555666
      # 预约提醒模板
      appointment-reminder: SMS_777888999
  
  # 腾讯云短信配置
  tencent:
    # 是否启用
    enabled: false
    # SecretId
    secret-id: ${SMS_TENCENT_SECRET_ID:your_secret_id}
    # SecretKey
    secret-key: ${SMS_TENCENT_SECRET_KEY:your_secret_key}
    # 短信应用ID
    app-id: ${SMS_TENCENT_APP_ID:your_app_id}
    # 短信签名
    sign-name: ${SMS_TENCENT_SIGN_NAME:德阳市中江县人民医院}
    # 地域
    region: ap-beijing
    # 短信模板配置
    templates:
      # 验证码模板
      verification-code: "123456"
      # 登录通知模板
      login-notice: "987654"
      # 系统通知模板
      system-notice: "111222"
  
  # 华为云短信配置
  huawei:
    # 是否启用
    enabled: false
    # Access Key ID
    access-key-id: ${SMS_HUAWEI_ACCESS_KEY_ID:your_access_key_id}
    # Secret Access Key
    secret-access-key: ${SMS_HUAWEI_SECRET_ACCESS_KEY:your_secret_access_key}
    # 短信签名
    sign-name: ${SMS_HUAWEI_SIGN_NAME:德阳市中江县人民医院}
    # 短信通道号
    sender: ${SMS_HUAWEI_SENDER:your_sender}
    # 地域
    region: cn-north-4
    # 短信模板配置
    templates:
      # 验证码模板
      verification-code: "your_template_id_1"
      # 登录通知模板
      login-notice: "your_template_id_2"
  
  # 容联云短信配置
  cloopen:
    # 是否启用
    enabled: false
    # 账户SID
    account-sid: ${SMS_CLOOPEN_ACCOUNT_SID:your_account_sid}
    # 认证令牌
    auth-token: ${SMS_CLOOPEN_AUTH_TOKEN:your_auth_token}
    # 应用ID
    app-id: ${SMS_CLOOPEN_APP_ID:your_app_id}
    # 短信模板配置
    templates:
      # 验证码模板
      verification-code: "123456"
      # 登录通知模板
      login-notice: "987654"
  
  # 网易云信短信配置
  netease:
    # 是否启用
    enabled: false
    # App Key
    app-key: ${SMS_NETEASE_APP_KEY:your_app_key}
    # App Secret
    app-secret: ${SMS_NETEASE_APP_SECRET:your_app_secret}
    # 短信模板配置
    templates:
      # 验证码模板
      verification-code: "123456"
      # 登录通知模板
      login-notice: "987654"
  
  # 京东云短信配置
  jdcloud:
    # 是否启用
    enabled: false
    # Access Key ID
    access-key-id: ${SMS_JDCLOUD_ACCESS_KEY_ID:your_access_key_id}
    # Secret Access Key
    secret-access-key: ${SMS_JDCLOUD_SECRET_ACCESS_KEY:your_secret_access_key}
    # 地域
    region: cn-north-1
    # 短信签名
    sign-id: ${SMS_JDCLOUD_SIGN_ID:your_sign_id}
    # 短信模板配置
    templates:
      # 验证码模板
      verification-code: "your_template_id"
  
  # 短信发送记录配置
  record:
    # 是否启用发送记录
    enabled: true
    # 记录存储方式：database（数据库）、file（文件）、redis（Redis）
    storage-type: database
    # 记录保留天数
    retention-days: 30
    # 数据库表名（当storage-type为database时）
    table-name: sms_send_record
  
  # 短信内容过滤配置
  content-filter:
    # 是否启用内容过滤
    enabled: true
    # 敏感词列表
    sensitive-words:
      - "违法"
      - "赌博"
      - "色情"
    # 是否启用长度检查
    length-check: true
    # 最大长度
    max-length: 500
  
  # 短信发送统计配置
  statistics:
    # 是否启用统计
    enabled: true
    # 统计数据存储方式：memory（内存）、redis（Redis）、database（数据库）
    storage-type: redis
    # 统计周期（分钟）
    period: 60
