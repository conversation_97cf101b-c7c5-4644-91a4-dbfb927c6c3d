# SMS4J 短信配置文件 - 中间件模式（简化版）
# 注意：当前为简化配置，SMS4J依赖加载完成后可启用完整配置

# 临时配置：短信服务基础参数
sms:
  # 是否启用短信服务
  enabled: true

  # 默认短信厂商
  default-supplier: aliyun

  # 阿里云短信配置
  aliyun:
    access-key-id: ${SMS_ALIYUN_ACCESS_KEY_ID:your_access_key_id}
    access-key-secret: ${SMS_ALIYUN_ACCESS_KEY_SECRET:your_access_key_secret}
    signature: ${SMS_ALIYUN_SIGNATURE:德阳市中江县人民医院}
    template-id: ${SMS_ALIYUN_TEMPLATE_ID:SMS_123456789}

  # 腾讯云短信配置
  tencent:
    secret-id: ${SMS_TENCENT_SECRET_ID:your_secret_id}
    secret-key: ${SMS_TENCENT_SECRET_KEY:your_secret_key}
    app-id: ${SMS_TENCENT_APP_ID:your_app_id}
    signature: ${SMS_TENCENT_SIGNATURE:德阳市中江县人民医院}
    template-id: ${SMS_TENCENT_TEMPLATE_ID:123456}

# 完整的SMS4J 3.3.5配置（基于正确的API）
# 当SMS4J依赖正确加载后，可以启用以下配置：
#sms:
#  config-type: yaml
#  restricted: false
#  account-max: 0
#  minute-max: 0
#  core-pool-size: 10
#  max-pool-size: 30
#  queue-capacity: 50
#  shutdown-strategy: true
#  http-log: false
#  is-print: true
#
#  blends:
#    aliyun-main:
#      supplier: alibaba
#      access-key-id: ${SMS_ALIYUN_ACCESS_KEY_ID}
#      access-key-secret: ${SMS_ALIYUN_ACCESS_KEY_SECRET}
#      signature: ${SMS_ALIYUN_SIGNATURE}
#      template-id: ${SMS_ALIYUN_TEMPLATE_ID}
#      request-url: dysmsapi.aliyuncs.com
#      restricted: false
#      # 模板配置
#      templates:
#        verification_code_template: SMS_123456789
#        login_notice_template: SMS_987654321
#        system_notice_template: SMS_111222333
#        appointment_reminder_template: SMS_444555666

# 注意事项：
# 1. SmsResponse对象没有getMessage()方法，使用toString()获取错误信息
# 2. sendMessage方法的第三个参数必须是LinkedHashMap<String, String>类型
# 3. 参数格式：smsBlend.sendMessage(phone, templateId, LinkedHashMap<String, String> params)
