package com.jp.med.mid.modules.sms.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.jp.med.mid.common.exception.AppException;
import com.jp.med.mid.modules.sms.dto.SmsDto;
import com.jp.med.mid.modules.sms.dto.VerificationCodeDto;
import com.jp.med.mid.modules.sms.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 短信服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    private static final String SMS_CODE_PREFIX = "sms:code:";
    private static final String SMS_LIMIT_PREFIX = "sms:limit:";

    // 默认使用的配置ID
    private static final String DEFAULT_CONFIG_ID = "aliyun-main";

    @Override
    public boolean sendSms(SmsDto smsDto) {
        try {
            log.info("开始发送短信，手机号：{}, 模板：{}", smsDto.getPhone(), smsDto.getTemplateId());

            // 检查发送频率限制
            if (!checkSendLimit(smsDto.getPhone())) {
                throw new AppException("发送频率过快，请稍后再试");
            }

            // 获取SMS4J实例，如果指定了供应商则使用指定的，否则使用默认的
            String configId = StrUtil.isNotBlank(smsDto.getSupplier()) ? smsDto.getSupplier() : DEFAULT_CONFIG_ID;
            SmsBlend smsBlend = SmsFactory.getSmsBlend(configId);

            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + configId);
            }

            SmsResponse response;

            if (StrUtil.isNotBlank(smsDto.getContent())) {
                // 发送文本短信
                response = smsBlend.sendMessage(smsDto.getPhone(), smsDto.getContent());
            } else if (StrUtil.isNotBlank(smsDto.getTemplateId()) && smsDto.getTemplateParams() != null) {
                // 发送模板短信
                response = smsBlend.sendMessage(smsDto.getPhone(), smsDto.getTemplateId(), smsDto.getTemplateParams());
            } else {
                // 使用快速发送（需要在配置中设置template-id）
                String message = smsDto.getTemplateParams() != null ?
                    smsDto.getTemplateParams().values().iterator().next().toString() : "验证码";
                response = smsBlend.sendMessage(smsDto.getPhone(), message);
            }

            if (response.isSuccess()) {
                log.info("短信发送成功，手机号：{}, 消息ID：{}", smsDto.getPhone(), response.getData());
                // 记录发送限制
                recordSendLimit(smsDto.getPhone());
                return true;
            } else {
                log.error("短信发送失败，手机号：{}, 错误信息：{}", smsDto.getPhone(), response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("短信发送异常，手机号：{}", smsDto.getPhone(), e);
            throw new AppException("短信发送失败：" + e.getMessage());
        }
    }

    @Override
    public boolean sendVerificationCode(String phone, String code) {
        try {
            // 检查发送频率限制
            if (!checkSendLimit(phone)) {
                throw new AppException("发送频率过快，请稍后再试");
            }

            // 获取SMS4J实例
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + DEFAULT_CONFIG_ID);
            }

            // 发送验证码短信
            SmsResponse response = smsBlend.sendMessage(phone, code);

            if (response.isSuccess()) {
                log.info("验证码发送成功，手机号：{}", phone);

                // 将验证码存储到Redis
                if (redisTemplate != null) {
                    String key = SMS_CODE_PREFIX + phone;
                    redisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);
                    log.info("验证码已存储到Redis，手机号：{}", phone);
                }

                // 记录发送限制
                recordSendLimit(phone);
                return true;
            } else {
                log.error("验证码发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("验证码发送异常，手机号：{}", phone, e);
            throw new AppException("验证码发送失败：" + e.getMessage());
        }
    }

    @Override
    public boolean sendVerificationCode(VerificationCodeDto verificationCodeDto) {
        // 生成验证码
        String code = generateVerificationCode(verificationCodeDto.getCodeType(), verificationCodeDto.getCodeLength());
        verificationCodeDto.setCode(code);
        
        // 发送短信
        boolean result = sendVerificationCode(verificationCodeDto.getPhone(), code);
        
        if (result && redisTemplate != null) {
            // 存储验证码信息到Redis
            String key = SMS_CODE_PREFIX + verificationCodeDto.getBusinessType() + ":" + verificationCodeDto.getPhone();
            Map<String, Object> codeInfo = new HashMap<>();
            codeInfo.put("code", code);
            codeInfo.put("phone", verificationCodeDto.getPhone());
            codeInfo.put("businessType", verificationCodeDto.getBusinessType());
            codeInfo.put("userId", verificationCodeDto.getUserId());
            codeInfo.put("createTime", System.currentTimeMillis());
            
            redisTemplate.opsForValue().set(key, codeInfo, verificationCodeDto.getExpireMinutes(), TimeUnit.MINUTES);
            log.info("验证码信息已存储，业务类型：{}, 手机号：{}", verificationCodeDto.getBusinessType(), verificationCodeDto.getPhone());
        }
        
        return result;
    }

    @Override
    public boolean sendLoginNotice(String phone, String loginTime, String loginLocation) {
        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            String message = String.format("您的账户于%s在%s登录，如非本人操作请及时修改密码", loginTime, loginLocation);
            SmsResponse response = smsBlend.sendMessage(phone, message);

            if (response.isSuccess()) {
                log.info("登录通知发送成功，手机号：{}", phone);
                return true;
            } else {
                log.error("登录通知发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("登录通知发送异常，手机号：{}", phone, e);
            return false;
        }
    }

    @Override
    public boolean sendSystemNotice(String phone, String content) {
        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            SmsResponse response = smsBlend.sendMessage(phone, content);

            if (response.isSuccess()) {
                log.info("系统通知发送成功，手机号：{}", phone);
                return true;
            } else {
                log.error("系统通知发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("系统通知发送异常，手机号：{}", phone, e);
            return false;
        }
    }

    @Override
    public boolean sendPasswordReset(String phone, String resetCode) {
        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            SmsResponse response = smsBlend.sendMessage(phone, resetCode);

            if (response.isSuccess()) {
                log.info("密码重置短信发送成功，手机号：{}", phone);
                return true;
            } else {
                log.error("密码重置短信发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("密码重置短信发送异常，手机号：{}", phone, e);
            return false;
        }
    }

    @Override
    public boolean sendAppointmentReminder(String phone, String patientName, String appointmentTime, String department) {
        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            String message = String.format("尊敬的%s，您预约的%s科室%s的就诊时间即将到来，请准时就诊",
                patientName, department, appointmentTime);
            SmsResponse response = smsBlend.sendMessage(phone, message);

            if (response.isSuccess()) {
                log.info("预约提醒发送成功，手机号：{}", phone);
                return true;
            } else {
                log.error("预约提醒发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("预约提醒发送异常，手机号：{}", phone, e);
            return false;
        }
    }

    @Override
    public boolean batchSendSms(List<SmsDto> smsDtoList) {
        try {
            log.info("开始批量发送短信，数量：{}", smsDtoList.size());

            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + DEFAULT_CONFIG_ID);
            }

            int successCount = 0;
            for (SmsDto smsDto : smsDtoList) {
                try {
                    if (sendSms(smsDto)) {
                        successCount++;
                    }
                    // 避免发送过快
                    Thread.sleep(100);
                } catch (Exception e) {
                    log.error("批量发送短信失败，手机号：{}", smsDto.getPhone(), e);
                }
            }

            log.info("批量发送短信完成，成功：{}/{}", successCount, smsDtoList.size());
            return successCount > 0;
        } catch (Exception e) {
            log.error("批量发送短信异常", e);
            return false;
        }
    }

    @Override
    public String querySmsStatus(String messageId) {
        try {
            // 这里需要根据具体的短信供应商实现状态查询
            // SMS4J框架可能需要扩展来支持状态查询
            log.info("查询短信状态，消息ID：{}", messageId);
            return "DELIVERED"; // 示例返回值
        } catch (Exception e) {
            log.error("查询短信状态失败，消息ID：{}", messageId, e);
            return "UNKNOWN";
        }
    }

    @Override
    public String getSmsBalance() {
        try {
            // 这里需要根据具体的短信供应商实现余额查询
            log.info("查询短信余额");
            return "余额充足"; // 示例返回值
        } catch (Exception e) {
            log.error("查询短信余额失败", e);
            return "查询失败";
        }
    }

    /**
     * 生成验证码
     */
    private String generateVerificationCode(Integer codeType, Integer codeLength) {
        switch (codeType) {
            case 1: // 纯数字
                return RandomUtil.randomNumbers(codeLength);
            case 2: // 纯字母
                return RandomUtil.randomString(codeLength).toUpperCase();
            case 3: // 数字+字母
                return RandomUtil.randomStringUpper(codeLength);
            default:
                return RandomUtil.randomNumbers(codeLength);
        }
    }

    /**
     * 检查发送频率限制
     */
    private boolean checkSendLimit(String phone) {
        if (redisTemplate == null) {
            return true;
        }
        
        String key = SMS_LIMIT_PREFIX + phone;
        String count = (String) redisTemplate.opsForValue().get(key);
        
        if (count == null) {
            return true;
        }
        
        // 每分钟最多发送5条
        return Integer.parseInt(count) < 5;
    }

    /**
     * 记录发送限制
     */
    private void recordSendLimit(String phone) {
        if (redisTemplate == null) {
            return;
        }
        
        String key = SMS_LIMIT_PREFIX + phone;
        String count = (String) redisTemplate.opsForValue().get(key);
        
        if (count == null) {
            redisTemplate.opsForValue().set(key, "1", 1, TimeUnit.MINUTES);
        } else {
            redisTemplate.opsForValue().increment(key);
        }
    }
}
