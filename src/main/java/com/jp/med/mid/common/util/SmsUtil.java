package com.jp.med.mid.common.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.jp.med.mid.common.exception.AppException;
import com.jp.med.mid.modules.sms.dto.SmsDto;
import com.jp.med.mid.modules.sms.dto.VerificationCodeDto;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 短信工具类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Component
public class SmsUtil {

    private static RedisTemplate<String, Object> redisTemplate;

    private static final String SMS_CODE_PREFIX = "sms:code:";
    private static final String SMS_LIMIT_PREFIX = "sms:limit:";
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final String DEFAULT_CONFIG_ID = "aliyun-main";

    @Autowired(required = false)
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        SmsUtil.redisTemplate = redisTemplate;
    }

    /**
     * 发送短信
     *
     * @param phone 手机号
     * @param content 短信内容
     * @return 发送结果
     */
    public static boolean sendSms(String phone, String content) {
        validatePhone(phone);

        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + DEFAULT_CONFIG_ID);
            }

            SmsResponse response = smsBlend.sendMessage(phone, content);

            if (response.isSuccess()) {
                log.info("短信发送成功，手机号：{}", phone);
                return true;
            } else {
                log.error("短信发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("短信发送异常，手机号：{}", phone, e);
            throw new AppException("短信发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送模板短信
     *
     * @param phone 手机号
     * @param templateId 模板ID
     * @param params 模板参数
     * @return 发送结果
     */
    public static boolean sendTemplateSms(String phone, String templateId, Map<String, Object> params) {
        validatePhone(phone);

        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + DEFAULT_CONFIG_ID);
            }

            SmsResponse response = smsBlend.sendMessage(phone, templateId, params);

            if (response.isSuccess()) {
                log.info("模板短信发送成功，手机号：{}, 模板：{}", phone, templateId);
                return true;
            } else {
                log.error("模板短信发送失败，手机号：{}, 模板：{}, 错误信息：{}", phone, templateId, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("模板短信发送异常，手机号：{}, 模板：{}", phone, templateId, e);
            throw new AppException("模板短信发送失败：" + e.getMessage());
        }
    }

    /**
     * 使用指定配置发送短信
     *
     * @param configId 配置ID
     * @param phone 手机号
     * @param content 短信内容
     * @return 发送结果
     */
    public static boolean sendSms(String configId, String phone, String content) {
        validatePhone(phone);

        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(configId);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + configId);
            }

            SmsResponse response = smsBlend.sendMessage(phone, content);

            if (response.isSuccess()) {
                log.info("短信发送成功，配置：{}, 手机号：{}", configId, phone);
                return true;
            } else {
                log.error("短信发送失败，配置：{}, 手机号：{}, 错误信息：{}", configId, phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("短信发送异常，配置：{}, 手机号：{}", configId, phone, e);
            throw new AppException("短信发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送验证码短信
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 发送结果
     */
    public static boolean sendVerificationCode(String phone, String code) {
        return sendVerificationCode(phone, code, 5);
    }

    /**
     * 发送验证码短信（指定过期时间）
     *
     * @param phone 手机号
     * @param code 验证码
     * @param expireMinutes 过期时间（分钟）
     * @return 发送结果
     */
    public static boolean sendVerificationCode(String phone, String code, int expireMinutes) {
        validatePhone(phone);

        if (StrUtil.isBlank(code)) {
            throw new AppException("验证码不能为空");
        }

        // 检查发送频率限制
        if (!checkSendLimit(phone)) {
            throw new AppException("发送频率过快，请稍后再试");
        }

        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + DEFAULT_CONFIG_ID);
            }

            // 直接发送验证码（SMS4J 3.x支持快速发送）
            SmsResponse response = smsBlend.sendMessage(phone, code);

            if (response.isSuccess()) {
                log.info("验证码发送成功，手机号：{}", phone);
                // 存储验证码到Redis
                storeVerificationCode(phone, code, expireMinutes);
                // 记录发送限制
                recordSendLimit(phone);
                return true;
            } else {
                log.error("验证码发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("验证码发送异常，手机号：{}", phone, e);
            throw new AppException("验证码发送失败：" + e.getMessage());
        }
    }

    /**
     * 生成并发送验证码
     * 
     * @param phone 手机号
     * @param length 验证码长度
     * @return 验证码
     */
    public static String generateAndSendCode(String phone, int length) {
        String code = RandomUtil.randomNumbers(length);
        boolean result = sendVerificationCode(phone, code);
        
        if (result) {
            return code;
        } else {
            throw new AppException("验证码发送失败");
        }
    }

    /**
     * 生成并发送验证码（默认6位）
     * 
     * @param phone 手机号
     * @return 验证码
     */
    public static String generateAndSendCode(String phone) {
        return generateAndSendCode(phone, 6);
    }

    /**
     * 验证验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 验证结果
     */
    public static boolean verifyCode(String phone, String code) {
        return verifyCode(phone, code, true);
    }

    /**
     * 验证验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @param deleteAfterVerify 验证后是否删除
     * @return 验证结果
     */
    public static boolean verifyCode(String phone, String code, boolean deleteAfterVerify) {
        if (redisTemplate == null) {
            log.warn("Redis未配置，无法验证验证码");
            return false;
        }
        
        String key = SMS_CODE_PREFIX + phone;
        String storedCode = (String) redisTemplate.opsForValue().get(key);
        
        if (storedCode == null) {
            log.warn("验证码不存在或已过期，手机号：{}", phone);
            return false;
        }
        
        boolean isValid = storedCode.equals(code);
        
        if (isValid && deleteAfterVerify) {
            redisTemplate.delete(key);
            log.info("验证码验证成功并已删除，手机号：{}", phone);
        } else if (isValid) {
            log.info("验证码验证成功，手机号：{}", phone);
        } else {
            log.warn("验证码验证失败，手机号：{}", phone);
        }
        
        return isValid;
    }

    /**
     * 发送登录通知
     *
     * @param phone 手机号
     * @param loginTime 登录时间
     * @param location 登录地点
     * @return 发送结果
     */
    public static boolean sendLoginNotice(String phone, String loginTime, String location) {
        String message = String.format("您的账户于%s在%s登录，如非本人操作请及时修改密码", loginTime, location);
        return sendSms(phone, message);
    }

    /**
     * 发送系统通知
     *
     * @param phone 手机号
     * @param content 通知内容
     * @return 发送结果
     */
    public static boolean sendSystemNotice(String phone, String content) {
        return sendSms(phone, content);
    }

    /**
     * 发送预约提醒
     *
     * @param phone 手机号
     * @param patientName 患者姓名
     * @param appointmentTime 预约时间
     * @param department 科室
     * @return 发送结果
     */
    public static boolean sendAppointmentReminder(String phone, String patientName,
                                                 String appointmentTime, String department) {
        String message = String.format("尊敬的%s，您预约的%s科室%s的就诊时间即将到来，请准时就诊",
            patientName, department, appointmentTime);
        return sendSms(phone, message);
    }

    /**
     * 验证手机号格式
     */
    private static void validatePhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            throw new AppException("手机号不能为空");
        }
        
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            throw new AppException("手机号格式不正确");
        }
    }

    /**
     * 存储验证码到Redis
     */
    private static void storeVerificationCode(String phone, String code, int expireMinutes) {
        if (redisTemplate != null) {
            String key = SMS_CODE_PREFIX + phone;
            redisTemplate.opsForValue().set(key, code, expireMinutes, TimeUnit.MINUTES);
            log.info("验证码已存储到Redis，手机号：{}, 过期时间：{}分钟", phone, expireMinutes);
        }
    }

    /**
     * 检查发送频率限制
     */
    private static boolean checkSendLimit(String phone) {
        if (redisTemplate == null) {
            return true;
        }
        
        String key = SMS_LIMIT_PREFIX + phone;
        String count = (String) redisTemplate.opsForValue().get(key);
        
        if (count == null) {
            return true;
        }
        
        // 每分钟最多发送5条
        return Integer.parseInt(count) < 5;
    }

    /**
     * 记录发送限制
     */
    private static void recordSendLimit(String phone) {
        if (redisTemplate == null) {
            return;
        }
        
        String key = SMS_LIMIT_PREFIX + phone;
        String count = (String) redisTemplate.opsForValue().get(key);
        
        if (count == null) {
            redisTemplate.opsForValue().set(key, "1", 1, TimeUnit.MINUTES);
        } else {
            redisTemplate.opsForValue().increment(key);
        }
    }
}
