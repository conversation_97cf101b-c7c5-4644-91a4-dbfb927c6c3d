# SMS4J 正确API使用说明

## 📋 **API问题修复**

根据你的反馈，我已经修复了SMS4J API使用中的问题：

### ❌ **错误的API用法**
```java
// 错误1：SmsResponse没有getMessage()方法
if (response.isSuccess()) {
    // ...
} else {
    log.error("错误信息：{}", response.getMessage()); // ❌ 这个方法不存在
}

// 错误2：第三个参数类型错误
Map<String, Object> params = new HashMap<>(); // ❌ 类型错误
SmsResponse response = smsBlend.sendMessage(phone, templateId, params);
```

### ✅ **正确的API用法**
```java
// 正确1：使用toString()或其他方式获取错误信息
if (response.isSuccess()) {
    log.info("发送成功：{}", response.getData());
} else {
    log.error("发送失败：{}", response.toString()); // ✅ 使用toString()
}

// 正确2：第三个参数必须是LinkedHashMap<String, String>
LinkedHashMap<String, String> params = new LinkedHashMap<>(); // ✅ 正确类型
params.put("code", "123456");
SmsResponse response = smsBlend.sendMessage(phone, templateId, params);
```

## 🔧 **修复后的代码结构**

### 1. **Sms4jRealUtil.java** - 基于正确API的工具类
```java
public static boolean sendTemplateSms(String phone, String templateId, LinkedHashMap<String, String> params) {
    try {
        SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
        
        // 正确的API调用
        SmsResponse response = smsBlend.sendMessage(phone, templateId, params);
        
        if (response != null && response.isSuccess()) {
            log.info("发送成功：{}", response.getData());
            return true;
        } else {
            // 使用toString()而不是getMessage()
            String errorInfo = response != null ? response.toString() : "响应为空";
            log.error("发送失败：{}", errorInfo);
            return false;
        }
    } catch (Exception e) {
        log.error("发送异常", e);
        return false;
    }
}
```

### 2. **参数构建示例**
```java
// 发送验证码
public static boolean sendVerificationCode(String phone, String code) {
    LinkedHashMap<String, String> params = new LinkedHashMap<>();
    params.put("code", code);
    return sendTemplateSms(phone, "verification_code_template", params);
}

// 发送登录通知
public static boolean sendLoginNotice(String phone, String loginTime, String location) {
    LinkedHashMap<String, String> params = new LinkedHashMap<>();
    params.put("time", loginTime);
    params.put("location", location);
    return sendTemplateSms(phone, "login_notice_template", params);
}
```

## 🚀 **启用真实API的步骤**

### 第一步：确认SMS4J依赖正确
```xml
<dependency>
    <groupId>org.dromara.sms4j</groupId>
    <artifactId>sms4j-spring-boot-starter</artifactId>
    <version>3.3.5</version>
</dependency>
```

### 第二步：在Sms4jRealUtil.java中启用真实API
取消注释以下代码块：
```java
/*
try {
    SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
    // ... 真实的API调用代码
} catch (Exception e) {
    // ... 异常处理
}
*/
```

### 第三步：配置短信模板
在配置文件中设置正确的模板ID：
```yaml
sms:
  blends:
    aliyun-main:
      templates:
        verification_code_template: SMS_123456789
        login_notice_template: SMS_987654321
        system_notice_template: SMS_111222333
```

## 📝 **API调用规范**

### 1. **方法签名**
```java
SmsResponse sendMessage(String phone, String templateId, LinkedHashMap<String, String> params)
```

### 2. **参数说明**
- `phone`: 手机号（String类型）
- `templateId`: 模板ID（String类型）
- `params`: 模板参数（必须是LinkedHashMap<String, String>类型）

### 3. **响应处理**
```java
if (response != null && response.isSuccess()) {
    // 成功处理
    Object data = response.getData();
} else {
    // 失败处理 - 注意没有getMessage()方法
    String error = response != null ? response.toString() : "响应为空";
}
```

## 🧪 **测试方法**

### 1. **使用测试控制器**
```bash
# 测试验证码发送
curl -X POST "http://localhost:9529/sms/test/code?phone=13800138000&code=123456"

# 测试登录通知
curl -X POST "http://localhost:9529/sms/test/login?phone=13800138000&loginTime=2024-01-01 10:00:00&location=北京"
```

### 2. **查看日志输出**
启用真实API后，查看控制台日志确认：
- SMS4J配置是否正确加载
- 短信发送请求是否成功
- 响应数据格式是否正确

## ⚠️ **注意事项**

1. **类型严格匹配**：第三个参数必须是`LinkedHashMap<String, String>`，不能是其他Map类型

2. **错误信息获取**：`SmsResponse`没有`getMessage()`方法，使用`toString()`或其他属性

3. **空值检查**：始终检查`response`是否为null

4. **模板参数**：确保模板参数的key与短信模板中的占位符匹配

5. **配置验证**：确保SMS4J配置文件中的模板ID与代码中使用的一致

## 🔄 **当前状态**

- ✅ **API调用已修复** - 使用正确的方法签名和参数类型
- ✅ **错误处理已修复** - 不再使用不存在的getMessage()方法
- ✅ **代码结构完整** - 提供了完整的工具类和服务实现
- ⏳ **等待启用** - 取消注释即可启用真实的SMS4J API

修复后的代码应该能够正确调用SMS4J的API，不会再出现方法不存在或参数类型错误的问题。
