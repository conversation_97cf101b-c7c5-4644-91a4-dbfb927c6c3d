package com.jp.med.mid.common.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.jp.med.mid.common.exception.AppException;
import com.jp.med.mid.modules.sms.dto.SmsDto;
import com.jp.med.mid.modules.sms.dto.VerificationCodeDto;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 短信工具类 - 中间件模式（无Redis依赖）
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Component
public class SmsUtil {

    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final String DEFAULT_CONFIG_ID = "aliyun-main";

    /**
     * 发送短信
     *
     * @param phone 手机号
     * @param content 短信内容
     * @return 发送结果
     */
    public static boolean sendSms(String phone, String content) {
        validatePhone(phone);

        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + DEFAULT_CONFIG_ID);
            }

            SmsResponse response = smsBlend.sendMessage(phone, content);

            if (response.isSuccess()) {
                log.info("短信发送成功，手机号：{}", phone);
                return true;
            } else {
                log.error("短信发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("短信发送异常，手机号：{}", phone, e);
            throw new AppException("短信发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送模板短信
     *
     * @param phone 手机号
     * @param templateId 模板ID
     * @param params 模板参数
     * @return 发送结果
     */
    public static boolean sendTemplateSms(String phone, String templateId, Map<String, Object> params) {
        validatePhone(phone);

        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + DEFAULT_CONFIG_ID);
            }

            SmsResponse response = smsBlend.sendMessage(phone, templateId, params);

            if (response.isSuccess()) {
                log.info("模板短信发送成功，手机号：{}, 模板：{}", phone, templateId);
                return true;
            } else {
                log.error("模板短信发送失败，手机号：{}, 模板：{}, 错误信息：{}", phone, templateId, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("模板短信发送异常，手机号：{}, 模板：{}", phone, templateId, e);
            throw new AppException("模板短信发送失败：" + e.getMessage());
        }
    }

    /**
     * 使用指定配置发送短信
     *
     * @param configId 配置ID
     * @param phone 手机号
     * @param content 短信内容
     * @return 发送结果
     */
    public static boolean sendSms(String configId, String phone, String content) {
        validatePhone(phone);

        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(configId);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + configId);
            }

            SmsResponse response = smsBlend.sendMessage(phone, content);

            if (response.isSuccess()) {
                log.info("短信发送成功，配置：{}, 手机号：{}", configId, phone);
                return true;
            } else {
                log.error("短信发送失败，配置：{}, 手机号：{}, 错误信息：{}", configId, phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("短信发送异常，配置：{}, 手机号：{}", configId, phone, e);
            throw new AppException("短信发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送验证码短信
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 发送结果
     */
    public static boolean sendVerificationCode(String phone, String code) {
        return sendVerificationCode(phone, code, 5);
    }

    /**
     * 发送验证码短信
     *
     * @param phone 手机号
     * @param code 验证码
     * @param expireMinutes 过期时间（分钟，仅用于日志记录）
     * @return 发送结果
     */
    public static boolean sendVerificationCode(String phone, String code, int expireMinutes) {
        validatePhone(phone);

        if (StrUtil.isBlank(code)) {
            throw new AppException("验证码不能为空");
        }

        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend(DEFAULT_CONFIG_ID);
            if (smsBlend == null) {
                throw new AppException("未找到短信配置：" + DEFAULT_CONFIG_ID);
            }

            // 直接发送验证码（SMS4J 3.x支持快速发送）
            SmsResponse response = smsBlend.sendMessage(phone, code);

            if (response.isSuccess()) {
                log.info("验证码发送成功，手机号：{}, 验证码：{}, 有效期：{}分钟", phone, code, expireMinutes);
                return true;
            } else {
                log.error("验证码发送失败，手机号：{}, 错误信息：{}", phone, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("验证码发送异常，手机号：{}", phone, e);
            throw new AppException("验证码发送失败：" + e.getMessage());
        }
    }

    /**
     * 生成并发送验证码
     * 
     * @param phone 手机号
     * @param length 验证码长度
     * @return 验证码
     */
    public static String generateAndSendCode(String phone, int length) {
        String code = RandomUtil.randomNumbers(length);
        boolean result = sendVerificationCode(phone, code);
        
        if (result) {
            return code;
        } else {
            throw new AppException("验证码发送失败");
        }
    }

    /**
     * 生成并发送验证码（默认6位）
     * 
     * @param phone 手机号
     * @return 验证码
     */
    public static String generateAndSendCode(String phone) {
        return generateAndSendCode(phone, 6);
    }

    /**
     * 中间件模式：不提供验证码验证功能
     * 验证码的验证应该由调用方（业务系统）负责
     *
     * @param phone 手机号
     * @param code 验证码
     * @return 始终返回false，提示调用方自行验证
     */
    @Deprecated
    public static boolean verifyCode(String phone, String code) {
        log.warn("中间件模式不提供验证码验证功能，请在业务系统中自行验证。手机号：{}", phone);
        return false;
    }

    /**
     * 发送登录通知
     *
     * @param phone 手机号
     * @param loginTime 登录时间
     * @param location 登录地点
     * @return 发送结果
     */
    public static boolean sendLoginNotice(String phone, String loginTime, String location) {
        String message = String.format("您的账户于%s在%s登录，如非本人操作请及时修改密码", loginTime, location);
        return sendSms(phone, message);
    }

    /**
     * 发送系统通知
     *
     * @param phone 手机号
     * @param content 通知内容
     * @return 发送结果
     */
    public static boolean sendSystemNotice(String phone, String content) {
        return sendSms(phone, content);
    }

    /**
     * 发送预约提醒
     *
     * @param phone 手机号
     * @param patientName 患者姓名
     * @param appointmentTime 预约时间
     * @param department 科室
     * @return 发送结果
     */
    public static boolean sendAppointmentReminder(String phone, String patientName,
                                                 String appointmentTime, String department) {
        String message = String.format("尊敬的%s，您预约的%s科室%s的就诊时间即将到来，请准时就诊",
            patientName, department, appointmentTime);
        return sendSms(phone, message);
    }

    /**
     * 验证手机号格式
     */
    private static void validatePhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            throw new AppException("手机号不能为空");
        }

        if (!PHONE_PATTERN.matcher(phone).matches()) {
            throw new AppException("手机号格式不正确");
        }
    }
}
